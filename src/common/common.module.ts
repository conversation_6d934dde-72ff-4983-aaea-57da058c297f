import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmailService } from './services/email.service';
import { DeviceInfoService } from './services/device-info.service';
import { ValidationService } from './services/validation.service';
import { PDFService } from './services/pdf.service';
import { VerificationService } from './services/verification.service';
import { Licenses } from '../entities/licenses.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Licenses])],
  providers: [EmailService, DeviceInfoService, ValidationService, PDFService, VerificationService],
  exports: [EmailService, DeviceInfoService, ValidationService, PDFService, VerificationService],
})
export class CommonModule {}
