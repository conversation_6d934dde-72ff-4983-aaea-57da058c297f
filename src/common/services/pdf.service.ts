import { Injectable, Logger } from '@nestjs/common';
import * as puppeteer from 'puppeteer';
import * as handlebars from 'handlebars';
import { readFileSync } from 'fs';
import { join } from 'path';
import * as qrcode from 'qrcode';

export interface PDFGenerationOptions {
  template: string;
  data: any;
  filename: string;
  format?: 'A4' | 'Letter';
  orientation?: 'portrait' | 'landscape';
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
}

export interface QRCodeOptions {
  text: string;
  width?: number;
  height?: number;
  margin?: number;
}

@Injectable()
export class PDFService {
  private readonly logger = new Logger(PDFService.name);
  private browser: puppeteer.Browser | null = null;

  constructor() {
    this.initializeBrowser();
  }

  private async initializeBrowser(): Promise<void> {
    try {
      this.browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });
      this.logger.log('PDF service browser initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize PDF service browser:', error);
    }
  }

  /**
   * Generate QR code as data URL
   */
  async generateQRCode(options: QRCodeOptions): Promise<string> {
    try {
      const qrOptions = {
        width: options.width || 200,
        height: options.height || 200,
        margin: options.margin || 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
      };

      return await qrcode.toDataURL(options.text, qrOptions);
    } catch (error) {
      this.logger.error('Failed to generate QR code:', error);
      throw new Error('QR code generation failed');
    }
  }

  /**
   * Generate PDF from Handlebars template
   */
  async generatePDF(options: PDFGenerationOptions): Promise<Buffer> {
    if (!this.browser) {
      await this.initializeBrowser();
    }

    if (!this.browser) {
      throw new Error('PDF service browser not available');
    }

    const page = await this.browser.newPage();

    try {
      // Load and compile template
      const templatePath = join(process.cwd(), 'src', 'templates', `${options.template}.hbs`);
      const templateContent = readFileSync(templatePath, 'utf8');
      const template = handlebars.compile(templateContent);

      // Generate QR code if verification URL is provided
      if (options.data.verificationUrl) {
        options.data.qrCodeDataUrl = await this.generateQRCode({
          text: options.data.verificationUrl,
        });
      }

      // Render HTML with data
      const html = template(options.data);

      // Set content and generate PDF
      await page.setContent(html, { waitUntil: 'networkidle0' });

      const pdfOptions: puppeteer.PDFOptions = {
        format: options.format || 'A4',
        landscape: options.orientation === 'landscape',
        margin: options.margin || {
          top: '20mm',
          right: '20mm',
          bottom: '20mm',
          left: '20mm',
        },
        printBackground: true,
        preferCSSPageSize: true,
      };

      const pdfBuffer = await page.pdf(pdfOptions);
      this.logger.log(`PDF generated successfully: ${options.filename}`);

      return Buffer.from(pdfBuffer);
    } catch (error) {
      this.logger.error(`Failed to generate PDF: ${options.filename}`, error);
      throw new Error(`PDF generation failed: ${error.message}`);
    } finally {
      await page.close();
    }
  }

  /**
   * Register Handlebars helpers
   */
  registerHelpers(): void {
    // Date formatting helper
    handlebars.registerHelper('formatDate', (date: Date | string, format?: string) => {
      const d = new Date(date);
      if (format === 'long') {
        return d.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
      }
      return d.toLocaleDateString();
    });

    // Currency formatting helper
    handlebars.registerHelper('formatCurrency', (amount: number | string) => {
      const num = typeof amount === 'string' ? parseFloat(amount) : amount;
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(num);
    });

    // Uppercase helper
    handlebars.registerHelper('uppercase', (str: string) => {
      return str ? str.toUpperCase() : '';
    });

    // Conditional helper
    handlebars.registerHelper('ifEquals', function(arg1, arg2, options) {
      return (arg1 == arg2) ? options.fn(this) : options.inverse(this);
    });

    this.logger.log('Handlebars helpers registered');
  }

  /**
   * Clean up resources
   */
  async onModuleDestroy(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.logger.log('PDF service browser closed');
    }
  }
}
