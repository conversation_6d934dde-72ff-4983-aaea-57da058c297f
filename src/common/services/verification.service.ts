import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Licenses, LicenseStatus } from '../../entities/licenses.entity';
import * as crypto from 'crypto';

export interface VerificationData {
  licenseId: string;
  licenseNumber: string;
  verificationCode: string;
  expiresAt: Date;
}

export interface PublicLicenseInfo {
  licenseNumber: string;
  licenseType: string;
  status: string;
  issueDate: Date;
  expiryDate: Date;
  organizationName: string;
  isValid: boolean;
  verifiedAt: Date;
}

@Injectable()
export class VerificationService {
  private readonly logger = new Logger(VerificationService.name);

  constructor(
    @InjectRepository(Licenses)
    private licensesRepository: Repository<Licenses>,
  ) {}

  /**
   * Generate a unique verification code for a license
   */
  generateVerificationCode(licenseId: string): string {
    const timestamp = Date.now().toString();
    const randomBytes = crypto.randomBytes(8).toString('hex');
    const hash = crypto
      .createHash('sha256')
      .update(`${licenseId}-${timestamp}-${randomBytes}`)
      .digest('hex');
    
    // Take first 12 characters for a shorter, more manageable code
    return hash.substring(0, 12).toUpperCase();
  }

  /**
   * Generate verification URL for a license
   */
  generateVerificationUrl(licenseId: string, licenseNumber: string): string {
    const verificationCode = this.generateVerificationCode(licenseId);
    const baseUrl = process.env.FRONTEND_URL || 'https://portal.macra.mw';
    
    // Create a public verification URL that doesn't require authentication
    return `${baseUrl}/public/verify/${licenseNumber}?code=${verificationCode}`;
  }

  /**
   * Verify a license using license number and verification code
   */
  async verifyLicense(licenseNumber: string, verificationCode?: string): Promise<PublicLicenseInfo | null> {
    try {
      const license = await this.licensesRepository.findOne({
        where: { license_number: licenseNumber },
        relations: ['applicant', 'license_type', 'issuer'],
      });

      if (!license) {
        this.logger.warn(`License verification failed: License not found - ${licenseNumber}`);
        return null;
      }

      // Check if license is active and not expired
      const isValid = license.status === LicenseStatus.ACTIVE && new Date() <= new Date(license.expiry_date);

      const publicInfo: PublicLicenseInfo = {
        licenseNumber: license.license_number,
        licenseType: license.license_type.name,
        status: license.status,
        issueDate: license.issue_date,
        expiryDate: license.expiry_date,
        organizationName: license.applicant.name,
        isValid,
        verifiedAt: new Date(),
      };

      this.logger.log(`License verified successfully: ${licenseNumber}`);
      return publicInfo;
    } catch (error) {
      this.logger.error(`License verification error for ${licenseNumber}:`, error);
      return null;
    }
  }

  /**
   * Get verification data for PDF generation
   */
  async getVerificationDataForPDF(licenseId: string, licenseNumber: string): Promise<VerificationData> {
    const verificationCode = this.generateVerificationCode(licenseId);
    const expiresAt = new Date();
    expiresAt.setFullYear(expiresAt.getFullYear() + 10); // Verification valid for 10 years

    return {
      licenseId,
      licenseNumber,
      verificationCode,
      expiresAt,
    };
  }

  /**
   * Validate verification code format
   */
  isValidVerificationCode(code: string): boolean {
    // Verification code should be 12 characters, alphanumeric, uppercase
    const pattern = /^[A-Z0-9]{12}$/;
    return pattern.test(code);
  }

  /**
   * Generate QR code data for license verification
   */
  generateQRCodeData(licenseId: string, licenseNumber: string): {
    verificationUrl: string;
    verificationCode: string;
  } {
    const verificationCode = this.generateVerificationCode(licenseId);
    const verificationUrl = this.generateVerificationUrl(licenseId, licenseNumber);

    return {
      verificationUrl,
      verificationCode,
    };
  }

  /**
   * Check if a license exists and is accessible for verification
   */
  async isLicenseVerifiable(licenseNumber: string): Promise<boolean> {
    try {
      const license = await this.licensesRepository.findOne({
        where: { license_number: licenseNumber },
        select: ['license_id', 'status'],
      });

      return !!license;
    } catch (error) {
      this.logger.error(`Error checking license verifiability for ${licenseNumber}:`, error);
      return false;
    }
  }

  /**
   * Get license verification statistics (for admin purposes)
   */
  async getVerificationStats(): Promise<{
    totalLicenses: number;
    activeLicenses: number;
    expiredLicenses: number;
    suspendedLicenses: number;
  }> {
    try {
      const [total, active, expired, suspended] = await Promise.all([
        this.licensesRepository.count(),
        this.licensesRepository.count({ where: { status: LicenseStatus.ACTIVE } }),
        this.licensesRepository.count({ where: { status: LicenseStatus.EXPIRED } }),
        this.licensesRepository.count({ where: { status: LicenseStatus.SUSPENDED } }),
      ]);

      return {
        totalLicenses: total,
        activeLicenses: active,
        expiredLicenses: expired,
        suspendedLicenses: suspended,
      };
    } catch (error) {
      this.logger.error('Error getting verification stats:', error);
      return {
        totalLicenses: 0,
        activeLicenses: 0,
        expiredLicenses: 0,
        suspendedLicenses: 0,
      };
    }
  }
}
