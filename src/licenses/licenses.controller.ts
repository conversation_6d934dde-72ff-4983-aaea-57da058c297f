import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
  <PERSON>s,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { LicensesService } from './licenses.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateLicenseDto } from '../dto/licenses/create-license.dto';
import { UpdateLicenseDto } from '../dto/licenses/update-license.dto';
import { Licenses } from '../entities/licenses.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';
import { Response } from 'express';

@ApiTags('Licences')
@Controller('licenses')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class LicensesController {
  constructor(private readonly licensesService: LicensesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new licence' })
  @ApiResponse({
    status: 201,
    description: 'Licence created successfully',
    type: Licenses,
  })
  @ApiResponse({
    status: 409,
    description: 'Licence with this number already exists',
  })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Created new licence',
  })
  async create(
    @Body() createLicenseDto: CreateLicenseDto,
    @Request() req: any,
  ): Promise<Licenses> {
    return this.licensesService.create(createLicenseDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all licences with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Licences retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Viewed licences list',
  })
  async findAll(
    @Paginate() query: PaginateQuery,
    @Request() req: any,
  ): Promise<PaginatedResult<Licenses>> {
    const userRoles = req.user?.roles || [];
    const userId = req.user?.userId;
    return this.licensesService.findAll(query, userRoles, userId);
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get licence statistics' })
  @ApiResponse({
    status: 200,
    description: 'Licence statistics retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Viewed licence statistics',
  })
  async getStats(): Promise<any> {
    return this.licensesService.getLicenseStats();
  }

  @Get('expiring-soon')
  @ApiOperation({ summary: 'Get licences expiring soon' })
  @ApiQuery({ name: 'days', required: false, description: 'Number of days ahead to check (default: 30)' })
  @ApiResponse({
    status: 200,
    description: 'Expiring licences retrieved successfully',
    type: [Licenses],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Viewed expiring licences',
  })
  async findExpiringSoon(@Query('days') days?: number): Promise<Licenses[]> {
    return this.licensesService.findExpiringSoon(days ? parseInt(days.toString()) : 30);
  }

  @Get('by-applicant/:applicantId')
  @ApiOperation({ summary: 'Get licences by applicant' })
  @ApiParam({ name: 'applicantId', description: 'Applicant UUID' })
  @ApiResponse({
    status: 200,
    description: 'Licences retrieved successfully',
    type: [Licenses],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Viewed licences by applicant',
  })
  async findByApplicant(@Param('applicantId', ParseUUIDPipe) applicantId: string): Promise<Licenses[]> {
    return this.licensesService.findByApplicant(applicantId);
  }

  @Get('by-number/:licenseNumber')
  @ApiOperation({ summary: 'Get licence by licence number' })
  @ApiParam({ name: 'licenseNumber', description: 'Licence number' })
  @ApiResponse({
    status: 200,
    description: 'Licence retrieved successfully',
    type: Licenses,
  })
  @ApiResponse({
    status: 404,
    description: 'Licence not found',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Viewed licence by number',
  })
  async findByLicenseNumber(@Param('licenseNumber') licenseNumber: string): Promise<Licenses> {
    return this.licensesService.findByLicenseNumber(licenseNumber);
  }

  @Get(':id/pdf')
  @ApiOperation({ summary: 'Generate and download licence PDF' })
  @ApiParam({ name: 'id', description: 'Licence UUID' })
  @ApiResponse({
    status: 200,
    description: 'PDF generated and downloaded successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Licence not found',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Generated licence PDF',
  })
  async generatePDF(
    @Param('id', ParseUUIDPipe) id: string,
    @Res() res: Response,
  ): Promise<void> {
    const license = await this.licensesService.findOne(id);
    const pdfBuffer = await this.licensesService.generateLicensePDF(id);

    const filename = `${license.license_number}_certificate.pdf`;

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Content-Length': pdfBuffer.length.toString(),
    });

    res.end(pdfBuffer);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get licence by ID' })
  @ApiParam({ name: 'id', description: 'Licence UUID' })
  @ApiResponse({
    status: 200,
    description: 'Licence retrieved successfully',
    type: Licenses,
  })
  @ApiResponse({
    status: 404,
    description: 'Licence not found',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Viewed licence details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Licenses> {
    return this.licensesService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update licence' })
  @ApiParam({ name: 'id', description: 'Licence UUID' })
  @ApiResponse({
    status: 200,
    description: 'Licence updated successfully',
    type: Licenses,
  })
  @ApiResponse({
    status: 404,
    description: 'Licence not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Licence with this number already exists',
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Updated licence',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateLicenseDto: UpdateLicenseDto,
    @Request() req: any,
  ): Promise<Licenses> {
    return this.licensesService.update(id, updateLicenseDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete licence' })
  @ApiParam({ name: 'id', description: 'Licence UUID' })
  @ApiResponse({
    status: 200,
    description: 'Licence deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Licence not found',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Deleted licence',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.licensesService.remove(id);
    return { message: 'Licence deleted successfully' };
  }
}
