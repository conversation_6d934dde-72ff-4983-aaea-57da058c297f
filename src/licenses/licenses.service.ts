import { Injectable, NotFoundException, ConflictException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Licenses, LicenseStatus } from '../entities/licenses.entity';
import { CreateLicenseDto } from '../dto/licenses/create-license.dto';
import { UpdateLicenseDto } from '../dto/licenses/update-license.dto';
import { PaginateQuery, PaginateConfig, paginate } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { PDFService } from '../common/services/pdf.service';
import { VerificationService } from '../common/services/verification.service';

@Injectable()
export class LicensesService {
  private readonly logger = new Logger(LicensesService.name);

  constructor(
    @InjectRepository(Licenses)
    private licensesRepository: Repository<Licenses>,
    private pdfService: PDFService,
    private verificationService: VerificationService,
  ) {
    // Register Handlebars helpers when service is initialized
    this.pdfService.registerHelpers();
  }

  private readonly paginateConfig: PaginateConfig<Licenses> = {
    sortableColumns: ['license_number', 'issue_date', 'expiry_date', 'status', 'created_at'],
    searchableColumns: ['license_number'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 10,
    maxLimit: 100,
    relations: ['applicant', 'license_type', 'application', 'issuer', 'creator', 'updater'],
  };

  async findAll(query: PaginateQuery, userRoles?: string[], userId?: string): Promise<PaginatedResult<Licenses>> {
    // Check if user is a customer (has customer role)
    const isCustomer = userRoles?.includes('customer');

    // If user is a customer, filter by their licenses only
    if (isCustomer && userId) {
      const customerQuery: PaginateQuery = {
        ...query,
        filter: {
          ...query.filter,
          applicant_id: userId // Assuming customer user_id matches applicant_id
        }
      };
      const result = await paginate(customerQuery, this.licensesRepository, this.paginateConfig);
      return PaginationTransformer.transform<Licenses>(result);
    }

    // For staff users, show all licenses
    const result = await paginate(query, this.licensesRepository, this.paginateConfig);
    return PaginationTransformer.transform<Licenses>(result);
  }

  async findOne(id: string): Promise<Licenses> {
    const license = await this.licensesRepository.findOne({
      where: { license_id: id },
      relations: ['applicant', 'license_type', 'application', 'issuer', 'creator', 'updater'],
    });

    if (!license) {
      throw new NotFoundException(`Licence with ID ${id} not found`);
    }

    return license;
  }

  async findByLicenseNumber(licenseNumber: string): Promise<Licenses> {
    const license = await this.licensesRepository.findOne({
      where: { license_number: licenseNumber },
      relations: ['applicant', 'license_type', 'application', 'issuer', 'creator', 'updater'],
    });

    if (!license) {
      throw new NotFoundException(`Licence with number ${licenseNumber} not found`);
    }

    return license;
  }

  async create(createLicenseDto: CreateLicenseDto, createdBy: string): Promise<Licenses> {
    // Check if licence number already exists
    const existingLicense = await this.licensesRepository.findOne({
      where: { license_number: createLicenseDto.license_number },
    });

    if (existingLicense) {
      throw new ConflictException(`Licence with number ${createLicenseDto.license_number} already exists`);
    }

    const license = this.licensesRepository.create({
      ...createLicenseDto,
      created_by: createdBy,
    });

    const savedLicense = await this.licensesRepository.save(license);
    this.logger.log(`License created successfully: ${savedLicense.license_number}`);

    return this.findOne(savedLicense.license_id);
  }

  async update(id: string, updateLicenseDto: UpdateLicenseDto, updatedBy: string): Promise<Licenses> {
    const license = await this.findOne(id);

    // Check if license number is being updated and if it conflicts
    if (updateLicenseDto.license_number && updateLicenseDto.license_number !== license.license_number) {
      const existingLicense = await this.licensesRepository.findOne({
        where: { license_number: updateLicenseDto.license_number },
      });

      if (existingLicense) {
        throw new ConflictException(`Licence with number ${updateLicenseDto.license_number} already exists`);
      }
    }

    await this.licensesRepository.update(id, {
      ...updateLicenseDto,
      updated_by: updatedBy,
    });

    this.logger.log(`License updated successfully: ${license.license_number}`);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const license = await this.findOne(id);
    await this.licensesRepository.softDelete(id);
    this.logger.log(`License deleted successfully: ${license.license_number}`);
  }

  /**
   * Generate PDF for a license based on its type
   */
  async generateLicensePDF(licenseId: string): Promise<Buffer> {
    const license = await this.findOne(licenseId);
    
    // Generate verification data
    const verificationData = this.verificationService.generateQRCodeData(
      license.license_id,
      license.license_number
    );

    // Determine template based on license type
    let template = 'standards-licence'; // default
    if (license.license_type.code === 'postal_services') {
      template = 'postal-licence';
    } else if (license.license_type.code === 'standards_compliance') {
      // Check if it's a short code allocation
      if (license.code) {
        template = 'short-code';
      }
    }

    // Prepare data for template
    const templateData = {
      licenseNumber: license.license_number,
      licenseType: license.license_type,
      status: license.status,
      issueDate: license.issue_date,
      expiryDate: license.expiry_date,
      applicant: license.applicant,
      issuer: license.issuer,
      conditions: license.conditions,
      code: license.code, // For short codes
      verificationUrl: verificationData.verificationUrl,
      verificationCode: verificationData.verificationCode,
    };

    // Add categories for postal services (if applicable)
    if (template === 'postal-licence') {
      // You would fetch license categories here based on the license
      // For now, we'll use a placeholder
      templateData['categories'] = [
        {
          name: 'Domestic Mail Services',
          description: 'Collection, processing, and delivery of domestic mail',
          authorizes: 'Provision of domestic postal services within Malawi'
        }
      ];
    }

    // Add category for short codes (if applicable)
    if (template === 'short-code' && license.code) {
      templateData['category'] = {
        name: 'Short Code Allocation',
        description: 'Allocation of short codes for SMS and USSD services',
        authorizes: 'Use of allocated short codes for commercial services'
      };
      templateData['serviceDescription'] = 'SMS and USSD services using allocated short code';
    }

    const filename = `${license.license_number}_certificate.pdf`;

    return this.pdfService.generatePDF({
      template,
      data: templateData,
      filename,
    });
  }

  /**
   * Get license statistics
   */
  async getLicenseStats(): Promise<any> {
    const stats = await this.licensesRepository
      .createQueryBuilder('license')
      .select('license.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('license.status')
      .getRawMany();

    return stats.reduce((acc, stat) => {
      acc[stat.status] = parseInt(stat.count);
      return acc;
    }, {});
  }

  /**
   * Get licenses by applicant
   */
  async findByApplicant(applicantId: string): Promise<Licenses[]> {
    return this.licensesRepository.find({
      where: { applicant_id: applicantId },
      relations: ['license_type', 'issuer'],
      order: { created_at: 'DESC' },
    });
  }

  /**
   * Get licenses expiring soon
   */
  async findExpiringSoon(days: number = 30): Promise<Licenses[]> {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    return this.licensesRepository
      .createQueryBuilder('license')
      .leftJoinAndSelect('license.applicant', 'applicant')
      .leftJoinAndSelect('license.license_type', 'license_type')
      .where('license.expiry_date <= :futureDate', { futureDate })
      .andWhere('license.expiry_date >= :today', { today: new Date() })
      .andWhere('license.status = :status', { status: LicenseStatus.ACTIVE })
      .orderBy('license.expiry_date', 'ASC')
      .getMany();
  }
}
